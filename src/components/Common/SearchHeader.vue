<template>
  <header class="search-header">
    <div class="search-header__input-wrapper" @click="handleInputClick">
      <!-- 搜索图标插槽 -->
      <slot name="search-icon">
        <img
          src="@/static/images/search.png"
          alt="搜索"
          class="search-header__icon"
          loading="lazy"
        />
      </slot>
      <input
        ref="inputRef"
        v-model="keyword"
        type="text"
        class="search-header__input"
        :placeholder="placeholder"
        :readonly="redirectToSearch"
        @keyup.enter="debouncedSearch"
      />
      <button
        type="button"
        class="search-header__button"
        @click.stop="debouncedSearch"
      >
        搜索
      </button>
    </div>
    <!-- 右侧操作区插槽 -->
    <slot name="right-action" />
  </header>
</template>

<script setup>
import { ref, watch, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { debounce } from 'lodash-es'

const router = useRouter()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '搜索'
  },
  redirectToSearch: {
    type: Boolean,
    default: false
  },
  redirectUrl: {
    type: String,
    default: '/search'
  }
})

const { modelValue, placeholder, redirectToSearch, redirectUrl } = toRefs(props)

const emit = defineEmits(['update:modelValue', 'search', 'clickable'])

// 响应式数据
const keyword = ref(modelValue.value)
const inputRef = ref(null)

// 监听modelValue变化
watch(modelValue, (newVal) => {
  keyword.value = newVal
}, { immediate: true })

// 监听内部值变化
watch(keyword, (newVal) => {
  emit('update:modelValue', newVal)
})

// 防抖搜索处理函数 - 性能优化
const debouncedSearch = debounce(() => {
  const trimmedKeyword = keyword.value?.trim()
  if (trimmedKeyword) {
    emit('search', trimmedKeyword)
  }
}, 300)

// 处理输入框点击事件
const handleInputClick = () => {
  if (redirectToSearch.value) {
    // 如果需要跳转，则跳转到指定页面
    router.push(redirectUrl.value)
  } else {
    // 如果不需要跳转，则聚焦输入框
    inputRef.value?.focus()
  }
  // 触发点击事件，供外部监听
  emit('clickable')
}

// 暴露给父组件的方法和属性
defineExpose({
  inputRef,
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur()
})
</script>

<style scoped lang="less">
.search-header {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #FFFFFF;
  box-sizing: border-box;

  &__input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    height: 32px;
    background-color: #FFFFFF;
    border-radius: 30px;
    padding: 3px 3px 3px 11px;
    box-sizing: border-box;
    border: 1px solid #E2E8EE;
    transition: border-color 0.2s ease;

    //&:focus-within {
    //  border-color: var(--wo-biz-theme-color);
    //}
  }

  &__icon {
    width: 13px;
    height: 13px;
    margin-right: 6px;
    flex-shrink: 0;
    image-rendering: -webkit-optimize-contrast;
  }

  &__input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 13px;
    color: #171E24;
    outline: none;
    min-width: 0;

    &::placeholder {
      color: #718096;
    }

    &::-webkit-input-placeholder {
      color: #718096;
    }

    &::-moz-placeholder {
      color: #718096;
    }
  }

  &__button {
    width: 50px;
    height: 26px;
    background-image: var(--wo-biz-theme-gradient-2);
    border-radius: 15px;
    font-size: 13px;
    font-weight: 500;
    text-align: center;
    color: #FFFFFF;
    border: none;
    cursor: pointer;
    flex-shrink: 0;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}
</style>
