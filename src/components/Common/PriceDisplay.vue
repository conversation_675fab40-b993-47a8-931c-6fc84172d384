<template>
  <!-- 价格区间显示 -->
  <span v-if="isRange" class="price-display price-range" :class="[sizeClass, colorClass, { 'price-bold': bold }]">
    <span class="range-label" v-if="rangeLabel">{{ rangeLabel }}：</span>
    <span class="price-item">
      <span class="currency">¥</span>
      <span class="integer">{{ lowIntegerPart }}</span>
      <span class="decimal" v-if="lowDecimalPart">.{{ lowDecimalPart }}</span>
    </span>
    <span class="range-separator"> - </span>
    <span class="price-item">
      <span class="currency">¥</span>
      <span class="integer">{{ highIntegerPart }}</span>
      <span class="decimal" v-if="highDecimalPart">.{{ highDecimalPart }}</span>
    </span>
  </span>
  <!-- 单个价格显示 -->
  <span v-else class="price-display" :class="[sizeClass, colorClass, { 'price-bold': bold }]">
    <span class="currency">¥</span>
    <span class="integer">{{ integerPart }}</span>
    <span class="decimal" v-if="decimalPart">.{{ decimalPart }}</span>
  </span>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  price: {
    type: [Number, String],
    required: false
  },
  highPrice: {
    type: [Number, String],
    required: false
  },
  lowPrice: {
    type: [Number, String],
    required: false
  },
  rangeLabel: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'medium',
    validator: value => ['small', 'medium', 'large'].includes(value)
  },
  color: {
    type: String,
    default: 'primary',
    validator: value => ['primary', 'orange', 'red', 'white'].includes(value)
  },
  bold: {
    type: Boolean,
    default: true
  }
})

// 判断是否为价格区间
const isRange = computed(() => {
  return (props.highPrice !== null && props.highPrice !== undefined) ||
    (props.lowPrice !== null && props.lowPrice !== undefined)
})

// 格式化价格的通用函数
const formatPrice = (price) => {
  if (price === null || price === undefined) {
    return '--.-'
  }
  // 如果价格已经是字符串格式（如 "¥100"），直接返回数字部分
  if (typeof price === 'string') {
    const numericPrice = parseFloat(price.replace(/[^\d.]/g, ''))
    return isNaN(numericPrice) ? '--.-' : (numericPrice/100).toFixed(2)
  }
  const priceValue = price / 100 // 假设传入的是分为单位
  return priceValue.toFixed(2)
}

// 单个价格的计算
const priceFormatted = computed(() => formatPrice(props.price))

const integerPart = computed(() => {
  if (props.price === null || props.price === undefined) {
    return '--'
  }
  return priceFormatted.value.split('.')[0]
})

const decimalPart = computed(() => {
  if (props.price === null || props.price === undefined) {
    return '-'
  }
  return priceFormatted.value.split('.')[1]
})

// 高价格的计算
const highPriceFormatted = computed(() => formatPrice(props.highPrice))

const highIntegerPart = computed(() => {
  if (props.highPrice === null || props.highPrice === undefined) {
    return '--'
  }
  return highPriceFormatted.value.split('.')[0]
})

const highDecimalPart = computed(() => {
  if (props.highPrice === null || props.highPrice === undefined) {
    return '-'
  }
  return highPriceFormatted.value.split('.')[1]
})

// 低价格的计算
const lowPriceFormatted = computed(() => formatPrice(props.lowPrice))

const lowIntegerPart = computed(() => {
  if (props.lowPrice === null || props.lowPrice === undefined) {
    return '--'
  }
  return lowPriceFormatted.value.split('.')[0]
})

const lowDecimalPart = computed(() => {
  if (props.lowPrice === null || props.lowPrice === undefined) {
    return '-'
  }
  return lowPriceFormatted.value.split('.')[1]
})

const sizeClass = computed(() => `price-${props.size}`)
const colorClass = computed(() => `price-${props.color}`)
</script>

<style scoped lang="less">
.price-display {
  font-family: 'D-DIN-PRO SemiBold';
  display: inline-flex;
  align-items: baseline;

  .currency {
    margin-right: 2px;
    font-weight: 400;
  }

  .integer {
    font-weight: 400;
  }

  .decimal {
    font-weight: 400;
  }

  // 尺寸变体
  &.price-small {
    .currency {
      font-size: 12px;
    }

    .integer {
      font-size: 16px;
    }

    .decimal {
      font-size: 12px;
    }
  }

  &.price-medium {
    .currency {
      font-size: 14px;
    }

    .integer {
      font-size: 20px;
    }

    .decimal {
      font-size: 14px;
    }
  }

  &.price-large {
    .currency {
      font-size: 16px;
    }

    .integer {
      font-size: 24px;
    }

    .decimal {
      font-size: 16px;
    }
  }

  // 颜色变体
  &.price-primary {
    color: #171E24;
  }

  &.price-orange {
    color: var(--wo-biz-theme-color);
  }

  &.price-red {
    color: var(--wo-biz-theme-color);
  }

  &.price-white {
    color: #FFFFFF;
  }

  // 加粗变体
  &.price-bold {
    .currency {
      font-weight: 600;
    }

    .integer {
      font-weight: 700;
    }

    .decimal {
      font-weight: 700;
    }
  }

  // 价格区间样式
  &.price-range {
    display: inline-flex;
    align-items: baseline;

    .range-label {
      //margin-right: 4px;
      font-size: 12px;
      font-weight: 400;
    }

    .price-item {
      display: inline-flex;
      align-items: baseline;
    }

    .range-separator {
      margin: 0 4px;
      font-size: inherit;
      font-weight: 400;
    }
  }
}
</style>
