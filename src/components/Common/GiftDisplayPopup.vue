<!--
  赠品展示弹窗组件
  功能：展示商品的赠品明细信息
  特性：
  - 显示赠品列表及其获得条件
  - 根据购买数量计算可获得的赠品数量
  - 支持不同的赠品获得规则计算
-->
<template>
  <van-popup
    v-model:show="visible"
    round
    position="bottom"
    :style="{ height: '80%', maxHeight: '80vh' }"
    @close="handleClose"
  >
    <div class="gift-display-popup">
      <!-- 弹窗头部 -->
      <div class="popup-header">
        <h3 class="title">商品明细</h3>
        <div class="close-btn" @click="handleClose">
          <img src="@/static/images/close.png" alt="关闭" />
        </div>
      </div>

      <!-- 提示信息 -->
      <div class="gift-tips">
        <span class="tips-text">商品数量有限，送完为止，请勿虚假购买</span>
      </div>

      <!-- 赠品列表 -->
      <div class="gift-list">
        <div
          v-for="item in giftList"
          :key="item.id"
          class="gift-item"
        >
          <div class="gift-image">
            <img
              :src="item.imagePath ? 'https://img13.360buyimg.com/n4/' + item.imagePath : item.listImageUrl"
              alt="赠品图片"
            >
          </div>
          <div class="gift-info">
            <div class="gift-info-left">
              <div class="gift-name">{{ item.skuName || item.name }}</div>
              <div
                class="gift-condition"
                v-if="shouldShowGiftCondition(item.belongToSkuMinNum)"
              >
                条件：{{ item.condition }}
              </div>
            </div>
            <div class="gift-info-right">
              <div
                v-if="isGiftAvailable(item.belongToSkuMinNum)"
                class="quantity"
              >
                x{{ calculateGiftQuantity(item.belongToSkuMaxNum, item.belongToSkuMinNum, item.giftNum) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <WoActionBar class="action-bar">
        <WoButton type="primary" block @click="handleClose">
          确定
        </WoButton>
      </WoActionBar>
    </div>
  </van-popup>
</template>

<script setup>
import { toRefs } from 'vue'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@/components/WoElementCom/WoActionBar.vue'

// 组件属性定义
const props = defineProps({
  // 弹窗显示状态
  visible: {
    type: Boolean,
    default: false
  },
  // 赠品列表数据
  giftList: {
    type: Array,
    default: () => []
  },
  // 商品购买数量
  goodsNum: {
    type: Number,
    default: 0
  }
})

// 解构 props 为响应式引用
const { giftList, goodsNum } = toRefs(props)

// 事件定义
const emit = defineEmits([
  'close',
  'update:visible'
])

// 判断是否需要显示赠品获得条件
const shouldShowGiftCondition = (minNum) => {
  return minNum !== '0'
}

// 判断赠品是否可获得（购买数量是否满足最小要求）
const isGiftAvailable = (minNum) => {
  return goodsNum.value >= Number(minNum)
}

// 计算赠品数量的核心逻辑
const calculateGiftQuantity = (maxNum, minNum, baseGiftNum) => {
  // 参数标准化处理
  const max = maxNum === undefined ? '0' : String(maxNum)
  const min = minNum === undefined ? '0' : String(minNum)
  const currentGoodsNum = goodsNum.value
  const giftMultiplier = Number(baseGiftNum)

  // 无限制条件：按购买数量倍数赠送
  if (max === '0' && min === '0') {
    return currentGoodsNum * giftMultiplier
  }

  // 只有最小限制：满足条件后按倍数赠送
  if (max === '0' && min !== '0') {
    const minRequired = Number(min)
    if (currentGoodsNum < minRequired) {
      return 0
    }
    return Math.floor(currentGoodsNum / minRequired) * giftMultiplier
  }

  // 只有最大限制：不超过最大值的倍数赠送
  if (max !== '0' && min === '0') {
    const maxAllowed = Number(max)
    if (currentGoodsNum > maxAllowed) {
      return maxAllowed * giftMultiplier
    }
    return currentGoodsNum * giftMultiplier
  }

  // 最小值大于最大值：无效配置
  if (Number(min) > Number(max)) {
    return 0
  }

  // 最小值等于最大值：固定赠送
  if (max !== '0' && min !== '0' && min === max) {
    return giftMultiplier
  }

  // 有最小和最大限制：分段计算
  if (Number(min) < Number(max)) {
    const minRequired = Number(min)
    const maxAllowed = Number(max)

    if (currentGoodsNum < minRequired) {
      return 0
    } else if (currentGoodsNum >= minRequired && currentGoodsNum < maxAllowed) {
      return Math.floor(currentGoodsNum / minRequired) * giftMultiplier
    } else if (currentGoodsNum >= maxAllowed) {
      return maxAllowed * giftMultiplier
    }
  }

  return 0
}

// 关闭弹窗处理
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}
</script>

<style scoped lang="less">
.gift-display-popup {
  background: #FFFFFF;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 16px 21px;
    flex-shrink: 0;

    .title {
      font-size: 17px;
      font-weight: 600;
      color: #171E24;
      margin: 0;
      text-align: center;
    }

    .close-btn {
      position: absolute;
      right: 20px;
      width: 24px;
      height: 24px;
      border: none;
      background: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;

      img {
        width: 24px;
        height: 24px;
      }

      &:hover {
        opacity: 0.7;
      }
    }
  }

  .gift-tips {
    padding: 0 17px 7px 17px;
    box-sizing: border-box;
    .tips-text {
      color: #FF4141;
      font-size: 12px;
      font-weight: 500;
      line-height: 1.5;
    }
  }

  .gift-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 21px 55px 21px;

    .gift-item {
      display: flex;
      background: #FFFFFF;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .gift-image {
        position: relative;
        width: 75px;
        height: 75px;
        margin-right: 17px;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 4px;
        }
      }

      .gift-info {
        flex: 1;
        display: flex;

        .gift-info-left {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin-right: 10px;

          .gift-name {
            font-size: 13px;
            color: #171E24;
            line-height: 1.5;
            display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
          }

          .gift-condition {
            font-size: 12px;
            color: #FF4141;
          }
        }

        .gift-info-right {
          .quantity {
            font-size: 13px;
            color: #718096;
          }
        }
      }
    }

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #F1F1F1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #C1C1C1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #A8A8A8;
    }
  }
}
</style>
