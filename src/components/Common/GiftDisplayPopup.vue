<template>
  <van-popup v-model:show="visible" round position="bottom" :style="{ height: '80%', maxHeight: '80vh' }" @close="handleClose">
    <div class="gift-display-popup">
      <!-- 弹窗头部 -->
      <div class="popup-header">
        <h3 class="title">商品明细</h3>
        <div class="close-btn" @click="handleClose">
          <img src="@/static/images/close.png" alt="关闭" />
        </div>
      </div>

      <!-- 提示信息 -->
      <div class="gift-tips">
        <span class="tips-text">商品数量有限，送完为止，请勿虚假购买</span>
      </div>

      <!-- 地址列表 -->
      <div class="gift-list" ref="giftListRef">
        <div v-for="item in giftList" :key="item.id" class="gift-item">
          <div class="gift-image">
            <img :src="item.imagePath ? 'https://img13.360buyimg.com/n4/' + item.imagePath : item.listImageUrl" alt="">
          </div>
          <div class="gift-info">
            <div class="gift-info-left">
              <div class="gift-name">{{ item.skuName || item.name }}</div>
              <div class="gift-condition" v-if="isGiftsAvailableIf(item.belongToSkuMinNum)">条件：{{ item.condition }}
              </div>
            </div>
            <div class="gift-info-right">
              <div v-if="isGiftsAvailable(item.belongToSkuMinNum)" class="quantity">x{{ giftNum(item.belongToSkuMaxNum,
                item.belongToSkuMinNum, item.giftNum) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <WoActionBar class="action-bar">
        <WoButton type="primary" block @click="handleClose">
          确定
        </WoButton>
      </WoActionBar>
    </div>
  </van-popup>
</template>

<script setup>
import { defineProps, defineEmits, toRefs } from 'vue'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@/components/WoElementCom/WoActionBar.vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  giftList: {
    type: Array,
    default: () => []
  },
  goodsNum: {
    type: Number,
    default: 0
  }
})

const { visible, giftList, goodsNum } = toRefs(props)

// Emits
const emit = defineEmits([
  'close',
  'select',
  'create',
  'edit',
  'delete',
  'update:visible'
])

// 判断是否展示赠品条件
const isGiftsAvailableIf = (giftBelongToSkuMinNum) => {
  return giftBelongToSkuMinNum !== '0'
}

// 判断赠品是否可用
const isGiftsAvailable = (giftBelongToSkuMinNum) => {
  return goodsNum.value >= giftBelongToSkuMinNum
}

// 计算赠品数量
const giftNum = (giftBelongToSkuMaxNum, giftBelongToSkuMinNum, giftNum) => {
  if (giftBelongToSkuMaxNum === undefined) {
    giftBelongToSkuMaxNum = '0'
  }
  if (giftBelongToSkuMinNum === undefined) {
    giftBelongToSkuMinNum = '0'
  }

  // 根据计算规则来
  if (giftBelongToSkuMaxNum === '0' && giftBelongToSkuMinNum === '0') {
    return goodsNum.value * giftNum
  }
  if (giftBelongToSkuMaxNum === '0' && giftBelongToSkuMinNum !== '0') {
    if (goodsNum.value < giftBelongToSkuMinNum) {
      return 0
    } else {
      return Math.floor(goodsNum.value / giftBelongToSkuMinNum) * giftNum
    }
  }
  if (giftBelongToSkuMaxNum !== '0' && giftBelongToSkuMinNum === '0') {
    if (goodsNum.value > giftBelongToSkuMinNum) {
      return giftBelongToSkuMinNum * giftNum
    } else {
      return goodsNum.value * giftNum
    }
  }
  if (giftBelongToSkuMinNum > giftBelongToSkuMaxNum) {
    return 0
  }
  if (giftBelongToSkuMaxNum !== '0' && giftBelongToSkuMinNum !== '0' && giftBelongToSkuMinNum === giftBelongToSkuMaxNum) {
    return 1 * giftNum
  }
  if (giftBelongToSkuMinNum < giftBelongToSkuMaxNum) {
    if (goodsNum.value < giftBelongToSkuMinNum) {
      return 0
    } else if (goodsNum.value >= giftBelongToSkuMinNum && goodsNum.value < giftBelongToSkuMaxNum) {
      return Math.floor(goodsNum.value / giftBelongToSkuMinNum) * giftNum
    } else if (goodsNum.value >= giftBelongToSkuMaxNum) {
      return giftBelongToSkuMaxNum * giftNum
    }
  }
  return 0
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}
</script>

<style scoped lang="less">
.gift-display-popup {
  background: #FFFFFF;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 16px 21px;
    flex-shrink: 0;

    .title {
      font-size: 17px;
      font-weight: 600;
      color: #171E24;
      margin: 0;
      text-align: center;
    }

    .close-btn {
      position: absolute;
      right: 20px;
      width: 24px;
      height: 24px;
      border: none;
      background: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;

      img {
        width: 24px;
        height: 24px;
      }

      &:hover {
        opacity: 0.7;
      }
    }
  }

  .gift-tips {
    padding: 0 17px 7px 17px;
    box-sizing: border-box;
    .tips-text {
      color: #FF4141;
      font-size: 12px;
      font-weight: 500;
      line-height: 1.5;
    }
  }

  .gift-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 21px 55px 21px;

    .gift-item {
      display: flex;
      background: #FFFFFF;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .gift-image {
        position: relative;
        width: 75px;
        height: 75px;
        margin-right: 17px;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 4px;
        }
      }

      .gift-info {
        flex: 1;
        display: flex;

        .gift-info-left {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin-right: 10px;

          .gift-name {
            font-size: 13px;
            color: #171E24;
            line-height: 1.5;
            display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
          }

          .gift-condition {
            font-size: 12px;
            color: #FF4141;
          }
        }

        .gift-info-right {
          .quantity {
            font-size: 13px;
            color: #718096;
          }
        }
      }
    }

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #F1F1F1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #C1C1C1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #A8A8A8;
    }
  }
}
</style>
