<template>
  <van-floating-bubble class="custom-back-top" magnetic="x" v-model:offset="offset">
    <template #default>
      <div class="floating-bubble-content">
        <div class="bubble-item" v-if="showBackTop">
          <img v-show="showBackTop" src="../../static/images/go-top.png" alt="回到顶部" @click="scrollToTop" />
        </div>
        <div class="bubble-item" v-if="isShowCart">
          <van-badge :content="cartCount" :show-zero="false" :max="99" class="cart-badge">
            <img :src="cartIcon" alt="购物车" @click="goToCart" />
          </van-badge>
        </div>
      </div>
    </template>
  </van-floating-bubble>
</template>

<script setup>
import { ref, onMounted, onUnmounted, toRefs, computed } from 'vue'
import { throttle } from 'lodash-es'
import { useNewCartStore } from '@/store/modules/newCart'
import { useUserStore } from '@/store/modules/user'

import { getBizCode } from '@utils/curEnv.js'
import woOneCart from '@/static/images/wo-one-cart.png'
import jdOneCart from '@/static/images/jd-one-cart.png'
const props = defineProps({
  offset: {
    type: Object,
    default: () => ({ bottom: 150 })
  },
  isShowCart: {
    type: Boolean,
    default: true
  },
  badgeContent: {
    type: [String, Number],
    default: ''
  }
})

const { offset, isShowCart } = toRefs(props)

const cartStore = useNewCartStore()
const userStore = useUserStore()

// 业务图片切换
const cartIcon = computed(() => (getBizCode() === 'ygjd' ? jdOneCart : woOneCart))

// 计算购物车数量，只在登录时显示
const cartCount = computed(() => {
  if (!userStore.isLogin) return 0
  return cartStore.countByGoods
})

const emit = defineEmits(['go-to-cart'])

const showBackTop = ref(false)

const handleScroll = throttle(() => {
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
  showBackTop.value = scrollTop > 300
}, 100)

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

const goToCart = () => {
  emit('go-to-cart')
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll, { passive: true })
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped lang="less">
.custom-back-top {
  :deep(.van-back-top__icon) {
    display: none;
  }

  .floating-bubble-content {
    max-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: transparent;
  }

  .bubble-item {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 50px;
    cursor: pointer;

    img {
      width: 44px;
      height: 44px;
    }

    .cart-badge {
      position: relative;

      :deep(.van-badge) {
        position: absolute;
        top: 5px;
        right: 9px;
        background: var(--wo-biz-theme-color);
        border: 2px solid #FFFFFF;
      }
    }
  }
}
</style>
