<!--
  浮动气泡组件
  功能：提供回到顶部和购物车快捷入口的悬浮按钮
  特性：
  - 支持磁性吸附到屏幕边缘
  - 滚动超过300px时显示回到顶部按钮
  - 显示购物车商品数量徽标
  - 根据业务类型切换购物车图标
-->
<template>
  <van-floating-bubble
    class="custom-back-top"
    magnetic="x"
    v-model:offset="offset"
  >
    <template #default>
      <div class="floating-bubble-content">
        <!-- 回到顶部按钮 -->
        <div class="bubble-item" v-if="showBackTop">
          <img
            src="../../static/images/go-top.png"
            alt="回到顶部"
            @click="scrollToTop"
          />
        </div>

        <!-- 购物车按钮 -->
        <div class="bubble-item" v-if="isShowCart">
          <van-badge
            :content="cartCount"
            :show-zero="false"
            :max="99"
            class="cart-badge"
          >
            <img
              :src="cartIcon"
              alt="购物车"
              @click="goToCart"
            />
          </van-badge>
        </div>
      </div>
    </template>
  </van-floating-bubble>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { throttle } from 'lodash-es'
import { useNewCartStore } from '@/store/modules/newCart'
import { useUserStore } from '@/store/modules/user'
import { getBizCode } from '@utils/curEnv.js'
import woOneCart from '@/static/images/wo-one-cart.png'
import jdOneCart from '@/static/images/jd-one-cart.png'

// 组件属性定义
const props = defineProps({
  // 浮动气泡位置偏移量
  offset: {
    type: Object,
    default: () => ({ bottom: 150 })
  },
  // 是否显示购物车按钮
  isShowCart: {
    type: Boolean,
    default: true
  }
})

// 事件定义
const emit = defineEmits(['go-to-cart'])

// Store 实例
const cartStore = useNewCartStore()
const userStore = useUserStore()

// 响应式数据
const showBackTop = ref(false)

// 计算属性 - 根据业务类型选择购物车图标
const cartIcon = computed(() => {
  return getBizCode() === 'ygjd' ? jdOneCart : woOneCart
})

// 计算属性 - 购物车商品数量（仅登录用户显示）
const cartCount = computed(() => {
  if (!userStore.isLogin) return 0
  return cartStore.countByGoods
})

// 滚动事件处理器（节流优化）
const handleScroll = throttle(() => {
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
  showBackTop.value = scrollTop > 300
}, 100)

// 回到顶部功能
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 跳转购物车功能
const goToCart = () => {
  emit('go-to-cart')
}

// 生命周期钩子 - 组件挂载时添加滚动监听
onMounted(() => {
  window.addEventListener('scroll', handleScroll, { passive: true })
})

// 生命周期钩子 - 组件卸载时移除滚动监听
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped lang="less">
.custom-back-top {
  :deep(.van-back-top__icon) {
    display: none;
  }

  .floating-bubble-content {
    max-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: transparent;
  }

  .bubble-item {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 50px;
    cursor: pointer;

    img {
      width: 44px;
      height: 44px;
    }

    .cart-badge {
      position: relative;

      :deep(.van-badge) {
        position: absolute;
        top: 5px;
        right: 9px;
        background: var(--wo-biz-theme-color);
        border: 2px solid #FFFFFF;
      }
    }
  }
}
</style>
