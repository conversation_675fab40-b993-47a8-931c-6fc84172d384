<template>
  <div class="row-section">
    <div class="label">{{ label }}</div>
    <div class="value" :class="valueClass">
      <slot name="value">
        <span v-if="!showArrow" class="value-text">{{ value }}</span>
        <template v-else>
          <span class="value-text">{{ value }}</span>
          <img
            v-if="showArrow"
            :src="arrowIcon || arrowIconImg"
            :alt="arrowAlt"
            class="arrow-icon"
          />
        </template>
      </slot>
    </div>
  </div>
</template>

<script setup>
import arrowIconImg from '../../static/images/arrow-right-gray.png'
defineProps({
  label: {
    type: String,
    required: true
  },
  value: {
    type: String,
    default: ''
  },
  valueClass: {
    type: [String, Array, Object],
    default: ''
  },
  showArrow: {
    type: Boolean,
    default: false
  },
  arrowIcon: {
    type: String,
    default: ''
  },
  arrowAlt: {
    type: String,
    default: '箭头'
  }
})
</script>

<style scoped lang="less">
.row-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    font-size: 13px;
    color: #4A5568;
    line-height: 1.5;
    min-width: 65px;
    flex-shrink: 0;
  }

  .value {
    font-size: 13px;
    color: #171E24;
    line-height: 1.5;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    margin-left: 12px;
    text-align: right;
    overflow: hidden;

    .value-text {
      overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
      max-width: 100%;
    }

    &.activity-text {
      color: var(--wo-biz-theme-color);
      font-weight: 500;
    }

    &.activity-no-text {
      color: #718096;
      font-weight: 400;
    }

    .arrow-icon {
      margin-left: 10px;
      width: 6px;
      height: 12px;
      color: #718096;
      font-size: 16px;
      vertical-align: middle;
      flex-shrink: 0;
    }
  }
}
</style>
