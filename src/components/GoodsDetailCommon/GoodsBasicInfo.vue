<template>
  <section class="basic-info-section">
    <div class="price-wrapper">
      <PriceDisplay :price="goodsInfo.price" size="large" color="orange"/>
      <span v-if="goodsInfo.originalPrice && goodsInfo.originalPrice > goodsInfo.price" class="price-original">
        ¥{{ goodsInfo.originalPrice }}
      </span>
    </div>

    <div class="title">
      {{ goodsInfo.name }}
    </div>
  </section>
</template>

<script setup>
import PriceDisplay from '@components/Common/PriceDisplay.vue'

defineProps({
  goodsInfo: {
    type: Object,
    required: true,
    default: () => ({
      name: '',
      price: 0,
      originalPrice: 0,
      imageUrl: ''
    })
  }
})
</script>

<style scoped lang="less">
.basic-info-section {
  background-color: #FFFFFF;
  padding: 10px 17px;
  box-sizing: border-box;
}

.price-wrapper {
  display: flex;
  align-items: baseline;
  gap: 12px;
  margin-bottom: 10px;

  .price-original {
    font-size: 14px;
    color: #999999;
    text-decoration: line-through;
  }
}

.title {
  font-size: 16px;
  color: #171E24;
  font-weight: 500;
  line-height: 1.5;
  display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
}
</style>
