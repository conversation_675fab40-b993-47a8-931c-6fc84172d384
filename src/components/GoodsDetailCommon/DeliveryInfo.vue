<template>
  <section class="delivery-section">
    <div class="delivery-wrapper">
      <div class="delivery-main" @click="handleAddressClick">
        <img src="./assets/logistics.png" alt="" class="delivery-icon" />
        <div class="delivery-content">
          <div class="delivery-location">
            <span class="delivery-text">{{ deliveryInfo.location }}</span>
            <img src="../../static/images/arrow-right-gray.png" alt="" class="arrow-icon" />
          </div>
          <div v-if="showLogisticsServices && bizCode !== 'zq'" class="delivery-time">
            <!-- 京东商品且有预测内容时显示 -->
            <template v-if="isJD && logisticsInfo.predictContent">
              <img v-if="logisticsInfo.logisticsType === 1" src="./assets/jdE.png" alt="" class="delivery-badge" />
              <img v-else-if="logisticsInfo.logisticsType === 0 || logisticsInfo.logisticsType === 2"
                src="./assets/threeE.png" alt="" class="delivery-badge" />
              <span class="delivery-text" v-html="logisticsInfo.predictContent"></span>
            </template>
            <!-- 非京东商品或无预测内容时的默认显示 -->
            <template v-else>
              <img src="./assets/threeE.png" alt="" class="delivery-badge" />
              <span class="delivery-text">预计48小时之内发货</span>
            </template>
            <img src="../../static/images/arrow-right-gray.png" alt="" class="arrow-icon" />
          </div>
        </div>
      </div>
      <div v-if="bizCode !== 'zq'" class="delivery-return">
        <img src="./assets/7days.png" alt="" class="return-icon" />
        <span class="delivery-text">{{ deliveryInfo.returnPolicy }}</span>
      </div>
      <div v-if="bizCode !== 'zq'" class="delivery-service">
        <img src="./assets/guarantee.png" alt="" class="service-icon" />
        <span class="delivery-text">店铺售后由<span class="text-highlight">沃百富商城</span>提供服务</span>
      </div>
    </div>
  </section>
</template>

<script setup>
import { getBizCode } from '@/utils/curEnv'

defineProps({
  deliveryInfo: {
    type: Object,
    required: true,
    default: () => ({
      location: '配送地址',
      predictContent: '预计48小时之内发货',
      returnPolicy: '7天无理由退货',
      service: '店铺售后由沃百富商城提供服务'
    })
  },
  logisticsInfo: {
    type: Object,
    default: () => ({
      logisticsType: 1,
      returnRuleStr: '',
      predictContent: '预计48小时之内发货',
      isJD: false
    })
  },
  isJD: {
    type: Boolean,
    default: false
  },
  showLogisticsServices: {
    type: Boolean,
    default: true
  }
})

const bizCode = getBizCode()

const emit = defineEmits(['address-click'])

const handleAddressClick = () => {
  emit('address-click')
}
</script>

<style scoped lang="less">
.delivery-section {
  background-color: #FFFFFF;
  padding: 5px 17px;
  box-sizing: border-box;
}

.arrow-icon {
      width: 5px;
      height: 9px;
      margin-left: 4px;
      flex-shrink: 0;
    }

.delivery-wrapper {
  .delivery-main {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;

    .delivery-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      margin-top: 2px;
    }

    .delivery-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;
      overflow: hidden;
    }
  }

  .delivery-location,
  .delivery-time,
  .delivery-return,
  .delivery-service {
    display: flex;
    align-items: center;
    font-size: 13px;
  }

  .delivery-location {
    display: flex;
    justify-content: space-between;
    .delivery-text {
      font-size: 13px;
      color: #171E24;
      font-weight: 500;
    }
  }

  .delivery-time {
    .delivery-badge {
      width: 64px;
      height: 20px;
      margin-right: 8px;
    }

    .delivery-text {
      color: #4A5568;
      flex: 1;
      overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
    }
  }



  .delivery-return {
    margin-bottom: 12px;

    .return-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .delivery-text {
      color: #4A5568;
    }
  }

  .delivery-service {
    .service-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .delivery-text {
      color: #4A5568;

      .text-highlight {
        color: var(--wo-biz-theme-color);
      }
    }
  }
}
</style>
