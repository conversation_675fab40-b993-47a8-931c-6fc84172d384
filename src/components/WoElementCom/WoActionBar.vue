<template>
  <div class="action-bar">
    <slot>
      <!-- 默认插槽内容 -->
      <WoButton type="primary" block :size="size" @click="handleAddClick">新增收货地址</WoButton>
    </slot>
  </div>
</template>

<script setup>
import { defineEmits, defineProps } from 'vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'

const props = defineProps({
  bottom: {
    type: [Number, String],
    default: 0
  },
  size: {
    type: String,
    default: 'default'
  }
})

const emit = defineEmits(['add'])

const handleAddClick = () => {
  emit('add')
}
</script>

<style scoped lang="less">
.action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: v-bind('props.bottom + "px"');
  padding: 10px 10px;
  background-color: #FFFFFF;
  //box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  z-index: 10;
  min-height: 55px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}
</style>
