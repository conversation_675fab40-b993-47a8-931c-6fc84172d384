<template>
  <div class="card-section">
    <div v-if="title" class="card-title">
      {{ title }}
    </div>
    <div class="card-content">
      <slot />
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: ''
  }
})
</script>

<style scoped lang="less">
.card-section {
  background-color: #FFFFFF;
  padding: 13px 15px;
  margin-bottom: 10px;
  border-radius: 10px;
}

.card-title {
  font-size: 15px;
  font-weight: 600;
  color: #171E24;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #E2E8EE;
}

.card-content {
  width: 100%;
}
</style>
