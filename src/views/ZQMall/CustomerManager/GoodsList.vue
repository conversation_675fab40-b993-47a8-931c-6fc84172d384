<template>
  <div class="goods-list-page">
    <header class="page-header" v-if="supplierList.length > 0">
      <Tabs v-model:active="curSupplierIndex" sticky @change="curSupplierChange">
        <Tab v-for="item in supplierList" :key="item.isvId" :title="item.isvName" />
      </Tabs>
      <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirectToSearch="false" @search="handleSearch"
        @clickable="handleSearchClick" />
    </header>

    <main class="goods-content">
      <!-- 供应商选择 -->
      <div v-if="supplierList.length === 0" class="empty">
        <img class="empty-img" src="./assets/empty1.png" alt="">
        <p class="empty-tips">暂无供应商</p>
      </div>

      <GoodsListLayout v-else :goods-list="goodsList" :is-loading="isLoading" :loading="loading" :finished="finished"
        empty-description="本地区无货" @load-more="onLoad" @item-click="goToDetail" @add-cart="addOneCart"
        @update:loading="(val) => loading = val" />
    </main>

    <FloatingBubble :offset="floatingBubbleOffset" :is-show-cart="false" @go-to-cart="goToCart" />

    <!-- 底部Tab -->
    <TabComponent currentTab="goods" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { showToast, Tab, Tabs } from 'vant'
import SearchHeader from '@components/Common/SearchHeader.vue'
import FloatingBubble from '@components/Common/FloatingBubble.vue'
import GoodsListLayout from '@components/GoodsListCommon/GoodsListLayout.vue'
import { useGoodsList } from '@/composables/useGoodsList.js'
import { getBizCode } from '@utils/curEnv.js'
import { zqQuerySimplified } from '@/api/index.js'
import { closeToast, showLoadingToast } from 'vant'
import { getCustomerManagerInfo, getEnterpriseManagerInfo, queryZqInfo } from '@utils/zqInfo.js'
import TabComponent from '@views/ZQMall/CustomerManager/components/Tab.vue'
import { get } from 'lodash-es'
import { useRoute, useRouter } from 'vue-router'

// 使用商品列表组合函数
const {
  goodsList,
  loading,
  finished,
  isLoading,
  pageNo,
  pageSize,
  resetList,
  processGoodsData,
  applyStockFilter,
  goToDetail,
  goToCart,
  addOneCart
} = useGoodsList()

// 在script setup中添加
const route = useRoute()
const router = useRouter()

// 页面特有状态
const floatingBubbleOffset = ref({ bottom: 150 })
const searchKeyword = ref('')
const categoryId = ref('')

// 新增的响应式数据
const supplierList = ref([])
const curSupplierIndex = ref(0)

// 修改zqInfo为方法
const zqInfo = () => {
  const zqInfoData = queryZqInfo()
  return {
    supplierCode: curSupplier.value.isvId ? curSupplier.value.isvId : '',
    proStr: zqInfoData.provinceCode ? zqInfoData.provinceCode.join(',') : ''
  }
}

// 计算属性
const curSupplier = computed(() => {
  return supplierList.value[curSupplierIndex.value] || {}
})

// 添加roleType的computed属性
const roleType = computed(() => {
  const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
  return roleType
})


// 转换后的handleSearch函数
const handleSearch = (keyWord) => {
  if (!keyWord) {
    showToast('请输入搜索内容')
    return
  }
  const timestamp = Date.parse(new Date())
  const testDMX = get(route.query, 'testDMX', false)
  const zqInfoData = zqInfo()
  router.push('/search/list?timestamp=' + timestamp + '&keyword=' + keyWord + '&testDMX=' + testDMX + '&supplierCode=' + zqInfoData.supplierCode + '&proStr=' + zqInfoData.proStr)
  searchKeyword.value = ''
}

const handleSearchClick = () => {
  console.log('搜索框被点击，即将跳转到搜索页面')
}

// 供应商切换处理
const curSupplierChange = () => {
  resetList()
  fetchGoodsList()
}

// 获取供应商列表
const getSupplierList = () => {
  const zqInfoData = queryZqInfo()
  supplierList.value = zqInfoData.isvList || []
}

// 获取商品列表
const fetchGoodsList = async () => {
  if (pageNo.value === 1) {
    isLoading.value = true
  }
  const zqInfoData = zqInfo()
  showLoadingToast()

  const [err, json] = await zqQuerySimplified({
    roleType: roleType.value,
    supplierCode: zqInfoData.supplierCode,
    proStr: zqInfoData.proStr,
    type: 3,
    bizCode: getBizCode('GOODS'),
    categoryId: categoryId.value,
    pageNo: pageNo.value,
    pageSize: pageSize.value
  })
  closeToast()

  if (pageNo.value === 1) {
    goodsList.value = []
  }

  loading.value = false
  isLoading.value = false

  if (!err) {
    if (json && json.goodsList && json.goodsList.length > 0) {
      const processedList = processGoodsData(json.goodsList)
      const filteredList = applyStockFilter(processedList)

      if (filteredList.length <= 0 && json.cacheType === '1') {
        pageNo.value++
        fetchGoodsList()
        return
      }

      goodsList.value = goodsList.value.concat(filteredList)

      if (json.cacheType === '1') {
        pageNo.value++
      } else {
        finished.value = true
      }
    } else {
      if (!json || (json && json.cacheType === '0')) {
        finished.value = true
        return
      }
      pageNo.value++
      fetchGoodsList()
    }
  } else {
    console.error('获取商品列表失败:', err.msg)
  }
}

const onLoad = () => {
  fetchGoodsList()
}

onMounted(async () => {
  // 初始化供应商列表
  getSupplierList()

  fetchGoodsList()
})
</script>

<style scoped lang="less">
.goods-list-page {
  padding-top: 88px;

  .page-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;

    :deep(.van-tabs) {
      .van-tab {
        color: #171E24;

        &.van-tab--active {
          color: var(--wo-biz-theme-color);
        }
      }

      .van-tabs__line {
        background-color: var(--wo-biz-theme-color);
      }
    }
  }

  .empty {
    font-size: 28px;
    color: #718096;
    text-align: center;
    margin: 0 auto;
    padding: 40px 0;

    .empty-img {
      width: 35%;
    }

    .empty-tips {
      font-size: 16px;
      margin-top: 16px;
    }
  }

  .goods-content {
    padding: 0 10px 50px 10px;
    box-sizing: border-box;
  }
}
</style>
