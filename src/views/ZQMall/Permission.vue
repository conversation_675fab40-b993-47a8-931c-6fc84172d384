<template>
  <div class="zq-permission">
    <template v-if="status < 0">
      <img src="@/assets/images/no-p.png" alt="无权限" class="zq-permission__icon" width="200" height="200" loading="eager" decoding="async" fetchpriority="high">
      <p class="zq-permission__text">暂无该业务查看权限</p>
    </template>
    <slot v-else />
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { queryZqInfo } from '@/utils/zqInfo'

const status = ref(0)
const userStore = useUserStore()

onMounted(async () => {
  const loginStatus = await userStore.login()
  if (loginStatus) {
    const zqInfo = queryZqInfo() || {}
    const roleType = zqInfo.roleType
    status.value = typeof roleType === 'number' && roleType < 0 ? roleType : 1
  } else {
    status.value = -99
  }
})
</script>

<style lang="less" scoped>
.zq-permission {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #FFFFFF;
}
.zq-permission__icon {
  width: 200px;
  height: 200px;
  margin-bottom: 10px;
}
.zq-permission__text {
  font-size: 14px;
  color: #718096;
  margin-bottom: 50px;
}
</style>
