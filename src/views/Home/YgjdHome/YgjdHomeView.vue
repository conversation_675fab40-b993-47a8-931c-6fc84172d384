<template>
  <BaseHomeLayout
    home-class="ygjd-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <template #additional-content>
      <div
        v-if="skeletonStates.recommend || hotGoods.length > 0"
        class="home-recommend-container"
      >
        <transition name="skeleton-fade" mode="out-in">
          <RecommendSkeleton v-if="skeletonStates.recommend" key="recommend-skeleton" />
          <RecommendView
            v-else-if="hotGoods.length > 0"
            key="recommend-content"
            :hotGoods="hotGoods"
          />
        </transition>
      </div>
    </template>

    <template #main-content>
      <WaterfallSection
        class="ygjd-waterfall-section"
        :waterfall-goods-list="waterfallGoodsList"
        :waterfall-loading="waterfallLoading"
        :waterfall-finished="waterfallFinished"
        :waterfall-button-can-show="waterfallButtonCanShow"
        :waterfall-render-complete="waterfallRenderComplete"
        :skeleton-states="{ waterfall: skeletonStates.waterfall }"
        @goods-click="handleGoodsClick"
        @load-more="handleWaterfallLoadMore"
        @after-render="handleWaterfallAfterRender"
      />
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import RecommendView from "@views/Home/components/RecommendView.vue"
import RecommendSkeleton from '@views/Home/components/Skeleton/RecommendSkeleton.vue'
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'
import { getHotGoods } from '@/api/interface/digitalVillage'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom } from 'commonkit'
import { closeToast, showLoadingToast } from 'vant'

// 使用组合式函数
const {
  headerBannerList,
  gridMenuItems,
  skeletonStates,
  moduleDataReady,
  waterfallGoodsList,
  waterfallLoading,
  waterfallFinished,
  waterfallButtonCanShow,
  waterfallRenderComplete,
  getHeaderBannerList,
  getIconList,
  getWaterfallList,
  resetWaterfallState,
  getPartionListData,
  hideSkeletonInOrder
} = useHomeData()

const {
  handleGoodsClick,
  handleBannerClick,
  handleGridItemClick,
  handleMoreClick,
  handleSearch
} = useHomeNavigation()

// 页面特有数据
const typeList = ref([])
const goodsPoolIdSelected = ref('')
const hotGoods = ref([])

// 扩展骨架屏状态
skeletonStates.value = {
  ...skeletonStates.value,
  recommend: true
}

moduleDataReady.value = {
  ...moduleDataReady.value,
  recommend: false
}

// 瀑布流渲染完成处理
const handleWaterfallAfterRender = () => {
  waterfallRenderComplete.value = true
}

// 加载更多瀑布流商品
const handleWaterfallLoadMore = () => {
  getWaterfallList(goodsPoolIdSelected.value, '', true)
}

// 切换商品池
const changeGoodsPool = (id, sortType = '') => {
  goodsPoolIdSelected.value = id
  resetWaterfallState()
  getWaterfallList(id, sortType, false)
}

// 初始化热门商品
const initHotGoods = async () => {
  const params = {
    showPage: '1',
    bizCode: getBizCode('GOODS'),
    channel: curChannelBiz.get()
  }

  if (!isUnicom) {
    showLoadingToast()
  }

  const [err, json] = await getHotGoods(params)

  if (!isUnicom) {
    closeToast()
  }

  if (err) {
    console.error('获取热门商品失败:', err.msg)
    moduleDataReady.value.recommend = true
    await hideSkeletonInOrder(['banner', 'gridMenu', 'recommend'])
    return
  }

  const arr = json || []
  hotGoods.value = arr

  moduleDataReady.value.recommend = true
  await hideSkeletonInOrder(['banner', 'gridMenu', 'recommend'])
}

// 初始化页面数据
const initPage = async () => {
  const partionList = await getPartionListData(2)
  typeList.value = partionList

  if (typeList.value.length > 0) {
    const recommond = typeList.value[0]
    goodsPoolIdSelected.value = recommond.id
    changeGoodsPool(recommond.id)
  }
}

onMounted(() => {
  getHeaderBannerList()
  getIconList(7) // YgjdHome 使用 showPage: 7
  initHotGoods()
  initPage()
})

onUnmounted(() => {
  // 清理工作
})
</script>

<style scoped lang="less">
.ygjd-home {
  //padding: 10px;
  //box-sizing: border-box;

  .home-recommend-container {
    padding: 0 10px;
    position: relative;
    box-sizing: border-box;
  }

  .ygjd-waterfall-section {
    :deep(.home-waterfall-container) {
      padding: 0 10px;
    }
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
